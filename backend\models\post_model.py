"""
MongoDB data models and database operations for social media posts
"""
import time
from datetime import datetime
from typing import List, Dict, Optional, Any
from pydantic import BaseModel, Field, validator
from pymongo import MongoClient, ASCENDING, DESCENDING
from pymongo.collection import Collection
from pymongo.database import Database
from pymongo.errors import Duplicate<PERSON>eyError, ConnectionFailure

from config.settings import get_settings
from utils.logger import get_logger

logger = get_logger(__name__)


class EngagementMetrics(BaseModel):
    """Model for engagement metrics across platforms"""
    likes: Optional[int] = 0
    retweets: Optional[int] = 0
    replies: Optional[int] = 0
    comments: Optional[int] = 0
    shares: Optional[int] = 0
    score: Optional[int] = 0  # Reddit score
    views: Optional[int] = 0


class SocialMediaPost(BaseModel):
    """Pydantic model for social media posts"""
    id: str = Field(..., description="Unique post ID from the platform")
    text: str = Field(..., description="Post content text")
    author: str = Field(..., description="Post author username")
    platform: str = Field(..., description="Platform name (twitter, reddit, youtube)")
    created_at: datetime = Field(..., description="Post creation timestamp")
    brand: str = Field(..., description="Brand being analyzed")
    
    # Sentiment analysis results
    sentiment_label: Optional[str] = Field(None, description="Sentiment label (positive, negative, neutral)")
    sentiment_score: Optional[float] = Field(None, description="Sentiment confidence score (0-1)")
    emotion_label: Optional[str] = Field(None, description="Emotion label (joy, anger, fear, etc.)")
    confidence_score: Optional[float] = Field(None, description="Overall confidence score")
    
    # Engagement metrics
    engagement_metrics: EngagementMetrics = Field(default_factory=EngagementMetrics)
    
    # Metadata
    analysis_timestamp: Optional[datetime] = Field(default_factory=datetime.utcnow)
    raw_data: Optional[Dict[str, Any]] = Field(None, description="Original raw data from API")
    
    @validator('platform')
    def validate_platform(cls, v):
        """Validate platform name"""
        valid_platforms = ['twitter', 'reddit', 'youtube']
        if v.lower() not in valid_platforms:
            raise ValueError(f'Platform must be one of: {valid_platforms}')
        return v.lower()
    
    @validator('sentiment_score', 'confidence_score')
    def validate_scores(cls, v):
        """Validate score ranges"""
        if v is not None and (v < 0 or v > 1):
            raise ValueError('Scores must be between 0 and 1')
        return v


class AnalysisRequest(BaseModel):
    """Model for analysis request"""
    brand: str = Field(..., description="Brand name to analyze")
    platforms: List[str] = Field(..., description="List of platforms to analyze")
    max_results_per_platform: int = Field(default=100, description="Maximum results per platform")
    
    @validator('platforms')
    def validate_platforms(cls, v):
        """Validate platform list"""
        valid_platforms = ['twitter', 'reddit', 'youtube']
        for platform in v:
            if platform.lower() not in valid_platforms:
                raise ValueError(f'Invalid platform: {platform}. Must be one of: {valid_platforms}')
        return [p.lower() for p in v]


class AnalysisResponse(BaseModel):
    """Model for analysis response"""
    brand: str
    total_posts: int
    platforms_analyzed: List[str]
    analysis_timestamp: datetime
    posts: List[SocialMediaPost]
    sentiment_summary: Dict[str, int]


class DatabaseManager:
    """MongoDB database manager with connection pooling"""
    
    def __init__(self):
        self.settings = get_settings()
        self.client: Optional[MongoClient] = None
        self.database: Optional[Database] = None
        self.collection: Optional[Collection] = None
        self._connect()
    
    def _connect(self):
        """Establish MongoDB connection"""
        try:
            if not self.settings.mongodb_connection_string:
                logger.warning("MongoDB connection string not provided. Database operations will be disabled.")
                return
            
            self.client = MongoClient(
                self.settings.mongodb_connection_string,
                maxPoolSize=50,
                minPoolSize=10,
                maxIdleTimeMS=30000,
                serverSelectionTimeoutMS=5000,
                connectTimeoutMS=10000,
                socketTimeoutMS=20000
            )
            
            # Test connection
            self.client.admin.command('ping')
            
            self.database = self.client[self.settings.mongodb_database_name]
            self.collection = self.database.analyzed_posts
            
            # Create indexes
            self._create_indexes()
            
            logger.info(f"Connected to MongoDB database: {self.settings.mongodb_database_name}")
            
        except ConnectionFailure as e:
            logger.error(f"Failed to connect to MongoDB: {e}")
            self.client = None
            self.database = None
            self.collection = None
        except Exception as e:
            logger.error(f"Unexpected error connecting to MongoDB: {e}")
            self.client = None
            self.database = None
            self.collection = None
    
    def _create_indexes(self):
        """Create database indexes for optimal performance"""
        if not self.collection:
            return
        
        try:
            # Compound index for efficient querying
            self.collection.create_index([
                ("brand", ASCENDING),
                ("platform", ASCENDING),
                ("created_at", DESCENDING)
            ])
            
            # Unique index to prevent duplicates
            self.collection.create_index([
                ("id", ASCENDING),
                ("platform", ASCENDING)
            ], unique=True)
            
            # Index for sentiment analysis
            self.collection.create_index([
                ("sentiment_label", ASCENDING),
                ("analysis_timestamp", DESCENDING)
            ])
            
            logger.info("Database indexes created successfully")
            
        except Exception as e:
            logger.error(f"Error creating database indexes: {e}")
    
    def is_connected(self) -> bool:
        """Check if database is connected"""
        return self.client is not None and self.database is not None
    
    def save_posts(self, posts: List[Dict]) -> bool:
        """
        Save posts to MongoDB
        
        Args:
            posts: List of post dictionaries
            
        Returns:
            True if successful, False otherwise
        """
        if not self.is_connected():
            logger.warning("Database not connected. Cannot save posts.")
            return False
        
        if not posts:
            logger.warning("No posts to save")
            return True
        
        try:
            start_time = time.time()
            
            # Convert to SocialMediaPost models for validation
            validated_posts = []
            for post_data in posts:
                try:
                    post = SocialMediaPost(**post_data)
                    validated_posts.append(post.dict())
                except Exception as e:
                    logger.error(f"Error validating post {post_data.get('id', 'unknown')}: {e}")
                    continue
            
            if not validated_posts:
                logger.warning("No valid posts to save after validation")
                return False
            
            # Insert posts (ignore duplicates)
            result = self.collection.insert_many(validated_posts, ordered=False)
            
            duration = time.time() - start_time
            logger.info(f"Saved {len(result.inserted_ids)} posts to database in {duration:.2f}s")
            
            return True
            
        except DuplicateKeyError:
            logger.info("Some posts already exist in database (duplicates ignored)")
            return True
        except Exception as e:
            logger.error(f"Error saving posts to database: {e}")
            return False

    def check_duplicate(self, post_id: str, platform: str) -> bool:
        """
        Check if a post already exists in the database

        Args:
            post_id: Post ID
            platform: Platform name

        Returns:
            True if post exists, False otherwise
        """
        if not self.is_connected():
            return False

        try:
            result = self.collection.find_one({
                "id": post_id,
                "platform": platform
            })
            return result is not None

        except Exception as e:
            logger.error(f"Error checking duplicate post: {e}")
            return False

    def get_posts_by_brand(self, brand: str, limit: int = 100, platform: str = None) -> List[Dict]:
        """
        Retrieve posts for a specific brand

        Args:
            brand: Brand name
            limit: Maximum number of posts to return
            platform: Optional platform filter

        Returns:
            List of post dictionaries
        """
        if not self.is_connected():
            logger.warning("Database not connected. Cannot retrieve posts.")
            return []

        try:
            start_time = time.time()

            # Build query
            query = {"brand": brand}
            if platform:
                query["platform"] = platform

            # Execute query with sorting and limit
            cursor = self.collection.find(query).sort("created_at", DESCENDING).limit(limit)
            posts = list(cursor)

            # Convert ObjectId to string for JSON serialization
            for post in posts:
                if "_id" in post:
                    post["_id"] = str(post["_id"])

            duration = time.time() - start_time
            logger.info(f"Retrieved {len(posts)} posts for brand '{brand}' in {duration:.2f}s")

            return posts

        except Exception as e:
            logger.error(f"Error retrieving posts for brand '{brand}': {e}")
            return []

    def get_sentiment_stats(self, brand: str, platform: str = None) -> Dict[str, int]:
        """
        Get sentiment statistics for a brand

        Args:
            brand: Brand name
            platform: Optional platform filter

        Returns:
            Dictionary with sentiment counts
        """
        if not self.is_connected():
            return {"positive": 0, "neutral": 0, "negative": 0}

        try:
            # Build query
            query = {"brand": brand, "sentiment_label": {"$exists": True}}
            if platform:
                query["platform"] = platform

            # Aggregate sentiment counts
            pipeline = [
                {"$match": query},
                {"$group": {
                    "_id": "$sentiment_label",
                    "count": {"$sum": 1}
                }}
            ]

            result = list(self.collection.aggregate(pipeline))

            # Format results
            stats = {"positive": 0, "neutral": 0, "negative": 0}
            for item in result:
                if item["_id"] in stats:
                    stats[item["_id"]] = item["count"]

            return stats

        except Exception as e:
            logger.error(f"Error getting sentiment stats for brand '{brand}': {e}")
            return {"positive": 0, "neutral": 0, "negative": 0}

    def close_connection(self):
        """Close database connection"""
        if self.client:
            self.client.close()
            logger.info("MongoDB connection closed")


# Global database manager instance
db_manager = DatabaseManager()
