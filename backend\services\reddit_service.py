"""
Reddit API integration service for fetching posts and comments
"""
import time
from datetime import datetime
from typing import List, Dict, Optional
import praw
from praw.exceptions import PRAWException, ResponseException
from prawcore.exceptions import RequestException, Forbidden, NotFound

from config.settings import get_settings
from utils.logger import get_logger, log_api_call, log_error_with_context, log_performance

logger = get_logger(__name__)


class RedditService:
    """Service for fetching Reddit posts and comments using PRAW"""
    
    def __init__(self):
        self.settings = get_settings()
        self.reddit: Optional[praw.Reddit] = None
        self.relevant_subreddits = [
            'technology', 'business', 'news', 'worldnews', 'stocks',
            'investing', 'entrepreneur', 'startups', 'gadgets',
            'cars', 'electricvehicles', 'teslamotors', 'apple',
            'android', 'gaming', 'pcmasterrace', 'buildapc'
        ]
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Reddit API client"""
        try:
            if not (self.settings.reddit_client_id and self.settings.reddit_client_secret):
                logger.warning("Reddit API credentials not provided. Reddit service will be disabled.")
                return
            
            self.reddit = praw.Reddit(
                client_id=self.settings.reddit_client_id,
                client_secret=self.settings.reddit_client_secret,
                user_agent=self.settings.reddit_user_agent,
                ratelimit_seconds=300  # 5 minutes between rate limit resets
            )
            
            # Test authentication by accessing user info
            try:
                # This will raise an exception if credentials are invalid
                self.reddit.user.me()
                logger.info("Reddit API client initialized successfully")
            except Exception:
                # For read-only access, we don't need user authentication
                # Just test if we can access a subreddit
                test_subreddit = self.reddit.subreddit('test')
                list(test_subreddit.hot(limit=1))
                logger.info("Reddit API client initialized successfully (read-only)")
                
        except Exception as e:
            logger.error(f"Error initializing Reddit client: {e}")
            self.reddit = None
    
    def is_available(self) -> bool:
        """Check if Reddit service is available"""
        return self.reddit is not None
    
    def fetch_reddit_posts(self, brand_keyword: str, limit: int = 100) -> List[Dict]:
        """
        Fetch Reddit posts mentioning a brand keyword
        
        Args:
            brand_keyword: Brand name or keyword to search for
            limit: Maximum number of posts to fetch
            
        Returns:
            List of standardized post dictionaries
        """
        if not self.is_available():
            logger.error("Reddit service not available")
            return []
        
        try:
            start_time = time.time()
            all_posts = []
            
            # Search across multiple subreddits
            posts_per_subreddit = max(1, limit // len(self.relevant_subreddits))
            
            for subreddit_name in self.relevant_subreddits:
                try:
                    subreddit_posts = self._search_subreddit(
                        subreddit_name, 
                        brand_keyword, 
                        posts_per_subreddit
                    )
                    all_posts.extend(subreddit_posts)
                    
                    # Rate limiting delay
                    if self.settings.reddit_rate_limit_delay > 0:
                        time.sleep(self.settings.reddit_rate_limit_delay)
                    
                    # Stop if we've reached the limit
                    if len(all_posts) >= limit:
                        break
                        
                except Exception as e:
                    logger.error(f"Error searching subreddit r/{subreddit_name}: {e}")
                    continue
            
            # Limit results
            all_posts = all_posts[:limit]
            
            duration = time.time() - start_time
            log_performance(logger, "fetch_reddit_posts", duration, len(all_posts))
            
            logger.info(f"Successfully fetched {len(all_posts)} Reddit posts for '{brand_keyword}'")
            
            return all_posts
            
        except Exception as e:
            log_error_with_context(logger, e, {"brand_keyword": brand_keyword, "limit": limit})
            return []
    
    def _search_subreddit(self, subreddit_name: str, brand_keyword: str, limit: int) -> List[Dict]:
        """
        Search for posts in a specific subreddit
        
        Args:
            subreddit_name: Name of the subreddit
            brand_keyword: Brand keyword to search for
            limit: Maximum number of posts to fetch
            
        Returns:
            List of post dictionaries
        """
        try:
            subreddit = self.reddit.subreddit(subreddit_name)
            
            log_api_call(logger, "reddit", f"search_subreddit_r/{subreddit_name}", {
                "query": brand_keyword,
                "limit": limit
            })
            
            # Search for posts
            search_results = subreddit.search(
                brand_keyword,
                sort='new',
                time_filter='month',
                limit=limit
            )
            
            posts = []
            for submission in search_results:
                try:
                    # Skip removed or deleted posts
                    if submission.selftext == '[removed]' or submission.selftext == '[deleted]':
                        continue
                    
                    # Get post text (title + selftext for text posts)
                    post_text = submission.title
                    if submission.selftext and submission.selftext.strip():
                        post_text += f" {submission.selftext}"
                    
                    # Create standardized post data
                    post_data = {
                        "id": f"reddit_{submission.id}",
                        "text": post_text,
                        "author": str(submission.author) if submission.author else "[deleted]",
                        "platform": "reddit",
                        "created_at": datetime.fromtimestamp(submission.created_utc),
                        "brand": brand_keyword,
                        "engagement_metrics": {
                            "score": submission.score,
                            "comments": submission.num_comments,
                            "likes": max(0, submission.score)  # Approximate upvotes
                        },
                        "raw_data": {
                            "submission_id": submission.id,
                            "subreddit": subreddit_name,
                            "url": submission.url,
                            "permalink": submission.permalink,
                            "is_self": submission.is_self,
                            "upvote_ratio": getattr(submission, 'upvote_ratio', None),
                            "gilded": getattr(submission, 'gilded', 0),
                            "stickied": getattr(submission, 'stickied', False)
                        }
                    }
                    
                    posts.append(post_data)
                    
                    # Also fetch top comments for this post
                    comments = self._fetch_post_comments(submission, brand_keyword, limit=3)
                    posts.extend(comments)
                    
                except Exception as e:
                    logger.error(f"Error processing Reddit post {submission.id}: {e}")
                    continue
            
            return posts
            
        except Forbidden:
            logger.warning(f"Access forbidden to subreddit r/{subreddit_name}")
            return []
        except NotFound:
            logger.warning(f"Subreddit r/{subreddit_name} not found")
            return []
        except Exception as e:
            logger.error(f"Error searching subreddit r/{subreddit_name}: {e}")
            return []
    
    def _fetch_post_comments(self, submission, brand_keyword: str, limit: int = 3) -> List[Dict]:
        """
        Fetch top comments from a Reddit post
        
        Args:
            submission: PRAW submission object
            brand_keyword: Brand keyword for context
            limit: Maximum number of comments to fetch
            
        Returns:
            List of comment dictionaries
        """
        try:
            # Get top-level comments
            submission.comments.replace_more(limit=0)  # Don't expand "more comments"
            top_comments = submission.comments[:limit]
            
            comments = []
            for comment in top_comments:
                try:
                    # Skip deleted/removed comments
                    if comment.body in ['[removed]', '[deleted]']:
                        continue
                    
                    # Skip very short comments
                    if len(comment.body.strip()) < 10:
                        continue
                    
                    comment_data = {
                        "id": f"reddit_comment_{comment.id}",
                        "text": comment.body,
                        "author": str(comment.author) if comment.author else "[deleted]",
                        "platform": "reddit",
                        "created_at": datetime.fromtimestamp(comment.created_utc),
                        "brand": brand_keyword,
                        "engagement_metrics": {
                            "score": comment.score,
                            "likes": max(0, comment.score)
                        },
                        "raw_data": {
                            "comment_id": comment.id,
                            "parent_id": submission.id,
                            "subreddit": submission.subreddit.display_name,
                            "permalink": comment.permalink,
                            "is_submitter": comment.is_submitter,
                            "gilded": getattr(comment, 'gilded', 0)
                        }
                    }
                    
                    comments.append(comment_data)
                    
                except Exception as e:
                    logger.error(f"Error processing comment {comment.id}: {e}")
                    continue
            
            return comments
            
        except Exception as e:
            logger.error(f"Error fetching comments for post {submission.id}: {e}")
            return []


# Global Reddit service instance
reddit_service = RedditService()
