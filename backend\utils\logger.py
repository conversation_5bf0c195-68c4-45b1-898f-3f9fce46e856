"""
Logging configuration for the Brand Sentiment Analysis Platform
"""
import logging
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

from config.settings import get_settings


class ColoredFormatter(logging.Formatter):
    """Custom formatter with colors for different log levels"""
    
    # ANSI color codes
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        # Add color to levelname
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


def setup_logger(
    name: str,
    log_file: Optional[str] = None,
    level: Optional[str] = None
) -> logging.Logger:
    """
    Set up a logger with both console and file handlers
    
    Args:
        name: Logger name (usually __name__)
        log_file: Optional log file path
        level: Log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    
    Returns:
        Configured logger instance
    """
    settings = get_settings()
    
    # Use provided level or fall back to settings
    log_level = level or settings.log_level
    
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, log_level))
    
    # Prevent duplicate handlers
    if logger.handlers:
        return logger
    
    # Create formatters
    console_formatter = ColoredFormatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    file_formatter = logging.Formatter(
        fmt='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, log_level))
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # File handler (if log_file is provided)
    if log_file:
        # Create logs directory if it doesn't exist
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(getattr(logging, log_level))
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance for a specific module
    
    Args:
        name: Logger name (usually __name__)
    
    Returns:
        Logger instance
    """
    settings = get_settings()
    
    # Create log file path based on current date
    log_file = f"logs/sentiment_analysis_{datetime.now().strftime('%Y%m%d')}.log"
    
    return setup_logger(name, log_file, settings.log_level)


# Application-wide logger instances
app_logger = get_logger("sentiment_analysis")
twitter_logger = get_logger("sentiment_analysis.twitter")
reddit_logger = get_logger("sentiment_analysis.reddit")
youtube_logger = get_logger("sentiment_analysis.youtube")
sentiment_logger = get_logger("sentiment_analysis.sentiment")
database_logger = get_logger("sentiment_analysis.database")


def log_api_call(logger: logging.Logger, platform: str, endpoint: str, params: dict):
    """Log API call details"""
    logger.info(f"API Call - Platform: {platform}, Endpoint: {endpoint}, Params: {params}")


def log_error_with_context(logger: logging.Logger, error: Exception, context: dict):
    """Log error with additional context"""
    logger.error(f"Error: {str(error)}, Context: {context}", exc_info=True)


def log_performance(logger: logging.Logger, operation: str, duration: float, count: int = None):
    """Log performance metrics"""
    if count:
        logger.info(f"Performance - Operation: {operation}, Duration: {duration:.2f}s, Count: {count}")
    else:
        logger.info(f"Performance - Operation: {operation}, Duration: {duration:.2f}s")
