# Twitter/X API v2 Credentials
TWITTER_BEARER_TOKEN=your_bearer_token_here

# Reddit API Credentials
REDDIT_CLIENT_ID=your_client_id_here
REDDIT_CLIENT_SECRET=your_client_secret_here
REDDIT_USER_AGENT=BrandSentimentBot/1.0

# YouTube Data API v3
YOUTUBE_API_KEY=your_api_key_here

# MongoDB Atlas Connection
MONGODB_CONNECTION_STRING=mongodb+srv://username:<EMAIL>/
MONGODB_DATABASE_NAME=brand_sentiment_db

# Application Settings
ENVIRONMENT=development
LOG_LEVEL=INFO
