"""
Configuration management using Pydantic BaseSettings
"""
import os
from typing import Optional
from pydantic import BaseSettings, Field, validator
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Settings(BaseSettings):
    """Application settings with environment variable validation"""
    
    # Application Settings
    environment: str = Field(default="development", env="ENVIRONMENT")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Twitter/X API v2 Credentials
    twitter_bearer_token: Optional[str] = Field(default=None, env="TWITTER_BEARER_TOKEN")
    
    # Reddit API Credentials
    reddit_client_id: Optional[str] = Field(default=None, env="REDDIT_CLIENT_ID")
    reddit_client_secret: Optional[str] = Field(default=None, env="REDDIT_CLIENT_SECRET")
    reddit_user_agent: str = Field(default="BrandSentimentBot/1.0", env="REDDIT_USER_AGENT")
    
    # YouTube Data API v3
    youtube_api_key: Optional[str] = Field(default=None, env="YOUTUBE_API_KEY")
    
    # MongoDB Configuration
    mongodb_connection_string: Optional[str] = Field(default=None, env="MONGODB_CONNECTION_STRING")
    mongodb_database_name: str = Field(default="brand_sentiment_db", env="MONGODB_DATABASE_NAME")
    
    # API Rate Limiting
    twitter_rate_limit_delay: int = Field(default=1, description="Delay between Twitter API calls in seconds")
    reddit_rate_limit_delay: int = Field(default=1, description="Delay between Reddit API calls in seconds")
    youtube_rate_limit_delay: int = Field(default=1, description="Delay between YouTube API calls in seconds")
    
    # Sentiment Analysis Settings
    sentiment_batch_size: int = Field(default=32, description="Batch size for sentiment analysis")
    max_text_length: int = Field(default=512, description="Maximum text length for analysis")
    
    @validator('log_level')
    def validate_log_level(cls, v):
        """Validate log level"""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'Log level must be one of: {valid_levels}')
        return v.upper()
    
    @validator('environment')
    def validate_environment(cls, v):
        """Validate environment"""
        valid_envs = ['development', 'production', 'testing']
        if v.lower() not in valid_envs:
            raise ValueError(f'Environment must be one of: {valid_envs}')
        return v.lower()
    
    def get_available_platforms(self) -> list:
        """Get list of available platforms based on configured API keys"""
        platforms = []
        if self.twitter_bearer_token:
            platforms.append("twitter")
        if self.reddit_client_id and self.reddit_client_secret:
            platforms.append("reddit")
        if self.youtube_api_key:
            platforms.append("youtube")
        return platforms
    
    def validate_platform_credentials(self, platform: str) -> bool:
        """Validate if credentials are available for a specific platform"""
        if platform == "twitter":
            return bool(self.twitter_bearer_token)
        elif platform == "reddit":
            return bool(self.reddit_client_id and self.reddit_client_secret)
        elif platform == "youtube":
            return bool(self.youtube_api_key)
        return False
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get application settings"""
    return settings
