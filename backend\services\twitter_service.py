"""
Twitter/X API v2 integration service for fetching tweets
"""
import time
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import tweepy
from tweepy.errors import TweepyException, TooManyRequests, Unauthorized, Forbidden

from config.settings import get_settings
from utils.logger import get_logger, log_api_call, log_error_with_context, log_performance

logger = get_logger(__name__)


class TwitterService:
    """Service for fetching tweets using Twitter API v2"""
    
    def __init__(self):
        self.settings = get_settings()
        self.client: Optional[tweepy.Client] = None
        self._initialize_client()
    
    def _initialize_client(self):
        """Initialize Twitter API client"""
        try:
            if not self.settings.twitter_bearer_token:
                logger.warning("Twitter Bearer Token not provided. Twitter service will be disabled.")
                return
            
            self.client = tweepy.Client(
                bearer_token=self.settings.twitter_bearer_token,
                wait_on_rate_limit=True
            )
            
            # Test authentication
            try:
                # Make a simple API call to verify credentials
                self.client.get_me()
                logger.info("Twitter API client initialized successfully")
            except Unauthorized:
                logger.error("Twitter API authentication failed. Please check your Bearer Token.")
                self.client = None
            except Exception as e:
                logger.error(f"Error testing Twitter API connection: {e}")
                self.client = None
                
        except Exception as e:
            logger.error(f"Error initializing Twitter client: {e}")
            self.client = None
    
    def is_available(self) -> bool:
        """Check if Twitter service is available"""
        return self.client is not None
    
    def fetch_tweets(self, brand_keyword: str, max_results: int = 100) -> List[Dict]:
        """
        Fetch tweets mentioning a brand keyword
        
        Args:
            brand_keyword: Brand name or keyword to search for
            max_results: Maximum number of tweets to fetch (10-100 per request)
            
        Returns:
            List of standardized tweet dictionaries
        """
        if not self.is_available():
            logger.error("Twitter service not available")
            return []
        
        try:
            start_time = time.time()
            
            # Prepare search query
            query = f'"{brand_keyword}" OR #{brand_keyword.replace(" ", "")} -is:retweet lang:en'
            
            # Limit max_results to API constraints
            max_results = min(max_results, 100)
            
            log_api_call(logger, "twitter", "search_recent_tweets", {
                "query": query,
                "max_results": max_results
            })
            
            # Fetch tweets
            tweets = tweepy.Paginator(
                self.client.search_recent_tweets,
                query=query,
                max_results=max_results,
                tweet_fields=['created_at', 'author_id', 'public_metrics', 'context_annotations', 'lang'],
                user_fields=['username', 'name', 'verified'],
                expansions=['author_id'],
                limit=1  # Only get one page for now
            ).flatten(limit=max_results)
            
            # Process tweets
            processed_tweets = []
            users_dict = {}
            
            # Get user information if available
            if hasattr(tweets, 'includes') and 'users' in tweets.includes:
                users_dict = {user.id: user for user in tweets.includes['users']}
            
            for tweet in tweets:
                try:
                    # Get user info
                    user = users_dict.get(tweet.author_id)
                    author_username = user.username if user else f"user_{tweet.author_id}"
                    
                    # Create standardized tweet data
                    tweet_data = {
                        "id": str(tweet.id),
                        "text": tweet.text,
                        "author": author_username,
                        "platform": "twitter",
                        "created_at": tweet.created_at,
                        "brand": brand_keyword,
                        "engagement_metrics": {
                            "likes": tweet.public_metrics.get('like_count', 0),
                            "retweets": tweet.public_metrics.get('retweet_count', 0),
                            "replies": tweet.public_metrics.get('reply_count', 0),
                            "views": tweet.public_metrics.get('impression_count', 0)
                        },
                        "raw_data": {
                            "tweet_id": str(tweet.id),
                            "author_id": str(tweet.author_id),
                            "lang": getattr(tweet, 'lang', 'en'),
                            "context_annotations": getattr(tweet, 'context_annotations', []),
                            "user_verified": user.verified if user else False
                        }
                    }
                    
                    processed_tweets.append(tweet_data)
                    
                except Exception as e:
                    logger.error(f"Error processing tweet {tweet.id}: {e}")
                    continue
            
            duration = time.time() - start_time
            log_performance(logger, "fetch_tweets", duration, len(processed_tweets))
            
            logger.info(f"Successfully fetched {len(processed_tweets)} tweets for '{brand_keyword}'")
            
            # Rate limiting delay
            if self.settings.twitter_rate_limit_delay > 0:
                time.sleep(self.settings.twitter_rate_limit_delay)
            
            return processed_tweets
            
        except TooManyRequests as e:
            logger.error(f"Twitter API rate limit exceeded: {e}")
            return []
        except Forbidden as e:
            logger.error(f"Twitter API access forbidden: {e}")
            return []
        except Unauthorized as e:
            logger.error(f"Twitter API unauthorized: {e}")
            return []
        except TweepyException as e:
            log_error_with_context(logger, e, {"brand_keyword": brand_keyword, "max_results": max_results})
            return []
        except Exception as e:
            log_error_with_context(logger, e, {"brand_keyword": brand_keyword, "max_results": max_results})
            return []
    
    def fetch_tweets_by_user(self, username: str, max_results: int = 50) -> List[Dict]:
        """
        Fetch recent tweets from a specific user
        
        Args:
            username: Twitter username (without @)
            max_results: Maximum number of tweets to fetch
            
        Returns:
            List of standardized tweet dictionaries
        """
        if not self.is_available():
            logger.error("Twitter service not available")
            return []
        
        try:
            start_time = time.time()
            
            # Get user ID
            user = self.client.get_user(username=username)
            if not user.data:
                logger.error(f"User '{username}' not found")
                return []
            
            user_id = user.data.id
            
            log_api_call(logger, "twitter", "get_users_tweets", {
                "user_id": user_id,
                "max_results": max_results
            })
            
            # Fetch user tweets
            tweets = self.client.get_users_tweets(
                id=user_id,
                max_results=min(max_results, 100),
                tweet_fields=['created_at', 'public_metrics', 'context_annotations', 'lang'],
                exclude=['retweets', 'replies']
            )
            
            if not tweets.data:
                logger.info(f"No tweets found for user '{username}'")
                return []
            
            # Process tweets
            processed_tweets = []
            for tweet in tweets.data:
                try:
                    tweet_data = {
                        "id": str(tweet.id),
                        "text": tweet.text,
                        "author": username,
                        "platform": "twitter",
                        "created_at": tweet.created_at,
                        "brand": username,  # Using username as brand for user-specific searches
                        "engagement_metrics": {
                            "likes": tweet.public_metrics.get('like_count', 0),
                            "retweets": tweet.public_metrics.get('retweet_count', 0),
                            "replies": tweet.public_metrics.get('reply_count', 0),
                            "views": tweet.public_metrics.get('impression_count', 0)
                        },
                        "raw_data": {
                            "tweet_id": str(tweet.id),
                            "author_id": str(user_id),
                            "lang": getattr(tweet, 'lang', 'en'),
                            "context_annotations": getattr(tweet, 'context_annotations', [])
                        }
                    }
                    
                    processed_tweets.append(tweet_data)
                    
                except Exception as e:
                    logger.error(f"Error processing tweet {tweet.id}: {e}")
                    continue
            
            duration = time.time() - start_time
            log_performance(logger, "fetch_tweets_by_user", duration, len(processed_tweets))
            
            logger.info(f"Successfully fetched {len(processed_tweets)} tweets from user '{username}'")
            
            return processed_tweets
            
        except Exception as e:
            log_error_with_context(logger, e, {"username": username, "max_results": max_results})
            return []


# Global Twitter service instance
twitter_service = TwitterService()
